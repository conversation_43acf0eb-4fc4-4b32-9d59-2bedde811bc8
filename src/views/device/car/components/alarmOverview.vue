<template>
    <div
        class="h-full bg-ff dark:bg-transparent rounded-lg alarm text-title dark:text-title-dark"
    >
        <div class="mb-3 leading-8 flex justify-between items-center">
            <div>{{ $t('alarm_yichangtongji') }}</div>
            <div
                class="space-x-3"
                v-if="$route.path == '/device/deviceDetail'"
            ></div>
        </div>
        <div class="overflow-hidden">
            <div class="echarts-all-box flex gap-x-6">
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-1" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chart1Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor:
                                        item.itemStyle.color.colorStops[0]
                                            .color,
                                }"
                            ></div>
                            <div class="w-18">{{ item.legendName }}</div>
                            <div>{{ item.value }}{{ $t('common_tiao') }}</div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-2" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chart2Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor:
                                        item.itemStyle.color.colorStops[0]
                                            .color,
                                }"
                            ></div>
                            <div class="w-18">{{ item.legendName }}</div>
                            <div>{{ item.value }}{{ $t('common_tiao') }}</div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-3" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chart3Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor:
                                        item.itemStyle.color.colorStops[0]
                                            .color,
                                }"
                            ></div>

                            <div
                                class="w-18 overflow-hidden overflow-ellipsis whitespace-nowrap"
                                :title="item.legendName"
                            >
                                {{ item.legendName }}
                            </div>

                            <div>{{ item.value }}{{ $t('common_tiao') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="table rounded-lg w-full">
            <search
                :title="$t('alarm_yichangliebiao')"
                :searchData="searchData"
                v-model:filter="searchData.filterData.value"
                @search="refresh"
                :showRefresh="false"
                class="teble-search"
            >
            </search>
            <div class="table-box">
                <mw-table
                    :dataSource="dataSource"
                    :columns="columns"
                    :hasPage="true"
                    :pageConfig="{ changePage, paginationProps }"
                    :customRow="Rowclick"
                    :loading="loading"
                    :rowKey="(record) => record.id"
                    @change="onTableChange"
                    :showRefresh="false"
                    class="alarm-table"
                >
                    <template #alarmDesc="{ record }">
                        <div
                            class="alarm-name font-medium text-primary-text dark:text-80-dark"
                        >
                            {{ record.alarmDesc || '-' }}
                        </div>
                        <div
                            class="alarm-sn text-secondar-text dark:text-60-dark"
                        >
                            {{ $t('Device No') }}：{{ record.deviceSn }}
                        </div>
                    </template>
                    <template #alarmStatus="{ record }">
                        <div>
                            <b
                                class="inline-block w-2 h-2 rounded-lg mr-1.5 mt-0"
                                :style="{
                                    backgroundColor: getAlarmStatus(
                                        record.alarmStatus
                                    ).backGroundColor,
                                    boxShadow: `0px 0px 2px 0px ${
                                        getAlarmStatus(record.alarmStatus)
                                            .backGroundColor
                                    }`,
                                }"
                            ></b>
                            <span class="text-primary-text dark:text-80-dark">
                                {{
                                    getAlarmStatus(record.alarmStatus).label
                                }}</span
                            >
                        </div>
                    </template>
                    <template #deviceType="{ record }">
                        <dictionary
                            :showBadge="false"
                            :statusOptions="deviceTypeList"
                            :value="record.deviceType"
                        />
                    </template>
                    <template #alarmLevel="{ record }">
                        <dictionary
                            :showBadge="false"
                            :statusOptions="alarmLevelList"
                            :value="record.alarmLevel"
                        />
                    </template>

                    <template #recoverTime="{ record }">
                        {{
                            record.recoverTime
                                ? dayjs(record.recoverTime).format(
                                      'YYYY-MM-DD HH:mm'
                                  )
                                : '-'
                        }}
                    </template>
                    <template #alarmTime="{ record }">
                        {{
                            record.alarmTime
                                ? dayjs(record.alarmTime).format(
                                      'YYYY-MM-DD HH:mm'
                                  )
                                : '-'
                        }}
                    </template>
                </mw-table>
            </div>
        </div>
        <alarmOverviewDetailDrawer
            v-model:visible="alarmOverviewDetailDrawerSwitch"
            :alarmId="alarmId"
            @success="refresh"
        >
        </alarmOverviewDetailDrawer>
    </div>
</template>

<script>
import * as echarts from 'echarts'
import {
    getPieOption,
    alarmLevelList,
    deviceTypeList,
    colors,
    DateOptionsMap,
} from '../../const'
import Search from '@/components/search/index.vue'
import {
    onMounted,
    reactive,
    ref,
    computed,
    watch,
    toRefs,
    getCurrentInstance,
} from 'vue'
// import AlarmForm from './components/alarmForm.vue'
import apiService from '@/apiService/device'
import powerApi from '@/apiService/power'
import { usePagenation } from '@/common/setup'
import alarmOverviewDetailDrawer from './alarmOverviewDetailDrawer.vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import dayjs from 'dayjs'
import Dictionary from '@/components/table/dictionary.vue'
import api from '@/apiService/strategy'
import { useI18n } from 'vue-i18n'
import { echartsColorVars, getThemeColor } from '@/common/util'
import useTheme from '@/common/useTheme'
export default {
    name: 'AlarmOverview',
    components: {
        // AlarmForm,
        Search,
        alarmOverviewDetailDrawer,
        Dictionary,
    },
    props: {
        getAlarmDataFlag: Boolean,
        stationId: String,
        stationType: {
            type: String,
            default: 'energy_storage_cabinet',
        },
    },
    setup(props) {
        const { t, locale } = useI18n()
        const { getAlarmDataFlag, stationType } = toRefs(props)
        const route = useRoute()

        const router = useRouter()

        const store = useStore()
        const { proxy } = getCurrentInstance()
        // const show = ref(true)

        const loading = ref(false)
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )

        const orgNameValue = ref(void 0)

        if (getCompanyInfo.value?.orgName) {
            orgNameValue.value = getCompanyInfo.value.orgName
        }
        const alarmId = ref()
        const alarmOverviewDetailDrawerSwitch = ref(false)
        const alarmStatusList = ref([
            {
                value: 0,
                label: t('daichuli'),
                color: '#FF5D5F',
                colorOffset: '#FF5D5F',
                backGroundColor: '#EA0C28',
            },
            {
                value: 1,
                label: t('yihuifu'),
                color: '#33be4f',
                colorOffset: '#33be4f',
                backGroundColor: '#33be4f',
            },
            // {
            //     value: 2,
            //     label: t('yihulue'),
            //     color: '#8AE6C5',
            //     colorOffset: '#8AE6C5',
            //     backGroundColor: '#4B82FF',
            // },
            // {
            //     value: 3,
            //     label: t('yibaozhang'),
            //     color: '#4492FE',
            //     colorOffset: '#4492FE',
            //     backGroundColor: '#8AD0FF',
            // },
        ])
        const echartsObj = reactive({
            'echarts-1': alarmStatusList.value,
            'echarts-2': alarmLevelList,
            'echarts-3': deviceTypeList,
        })
        const columns = [
            {
                title: t('alarm_yichangmingcheng'),
                dataIndex: 'alarmDesc',
                key: 'alarmDesc',
                width: 330,
                slots: {
                    customRender: 'alarmDesc',
                },
            },
            {
                title: t('BatteryNo'),
                dataIndex: 'batteryNo',
                key: 'batteryNo',
                width: 200,
            },
            {
                title: t('Project name'),
                dataIndex: 'projectName',
                key: 'projectName',
                width: 240,
            },

            {
                title: t('alarm_yichangzhuangtai'),
                dataIndex: 'alarmStatus',
                key: 'alarmStatus',
                slots: {
                    customRender: 'alarmStatus',
                },
                width: 160,
                align: 'center',
            },

            {
                title: t('yichangjibie'),
                dataIndex: 'alarmLevel',
                key: 'alarmLevel',
                slots: {
                    customRender: 'alarmLevel',
                },
                align: 'center',
            },
            {
                title: t('yichangshijian'),
                dataIndex: 'alarmTime',
                key: 'alarmTime',
                slots: {
                    customRender: 'alarmTime',
                },
                align: 'center',
            },
            {
                title: t('Recovery time'),
                dataIndex: 'recoverTime',
                key: 'recoverTime',
                slots: {
                    customRender: 'recoverTime',
                },
                align: 'right',
            },
        ]
        // 根据路由判读是否展示名称搜索框

        // const alarmOverviewDetailDrawerSwitch1 = ref(true)
        const searchData = reactive({
            fields: {
                rangeDate: {
                    ELType: 'el-date-picker',
                    value: [
                        dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
                        dayjs().format('YYYY-MM-DD'),
                    ],
                    valueFormat: 'YYYY-MM-DD',
                    width: '240px',
                    // separator: '—',
                    icon: 'iconfont icon-a-1',
                    allowClear: true,
                },
                alarmStatus: {
                    ELType: 'el-select',
                    options: [
                        {
                            value: '',
                            label: t('alarm_options_quanbuzhuangtai'),
                        },
                        ...alarmStatusList.value,
                    ],
                    placeholder: t('alarm_yichangzhuangtai'),
                    value: 0,
                },
                // deviceType: {
                //     ELType: 'el-select',
                //     options: [
                //         { value: '', label: t('alarm_quanbuleixing') },
                //         {
                //             label: 'BMS',
                //             value: 'BMS',
                //         },
                //         {
                //             label: t('Cell'),
                //             value: 'Cell',
                //         },
                //     ],
                //     placeholder: t('Device Type'),
                //     // allowClear: true,
                // },

                alarmLevel: {
                    ELType: 'el-select',
                    placeholder: t('yichangjibie'),
                    options: [
                        { value: '', label: t('All Level') },
                        ...alarmLevelList,
                    ],
                    // allowClear: true,
                },
            },
            filterData: {
                value: {
                    // filterValue: route?.query?.stationName
                    //     ? route.query.stationName
                    //     : '',
                    // filterBy: 'stationName',
                },

                options: {},

                config: {
                    filterByWidth: '120px',
                },
            },
        })

        const dataSource = ref([])
        const projectId = computed(() => {
            return route?.query?.projectId || undefined
        })
        const customerId = computed(() => {
            return route?.query?.customerId || undefined
        })
        const PostDeviceAlarmPage = async () => {
            let searchParam = {
                ...pageParam.value,
            }
            loading.value = true
            //搜索信息2
            for (const key in searchData.fields) {
                if (
                    key == 'rangeDate' &&
                    searchData.fields.rangeDate.value?.length > 0 > 0
                ) {
                    searchParam.startDate = searchData.fields.rangeDate.value[0]
                    searchParam.endDate = searchData.fields.rangeDate.value[1]
                } else {
                    searchParam[key] = searchData.fields[key].value
                }
            }
            // //单个搜索信息
            searchParam[searchData.filterData.value.filterBy] =
                searchData.filterData.value.filterValue
            try {
                const {
                    data: { data, code },
                } = await powerApi.powerDeviceAlarmPage({
                    stationNo: route?.query?.stationNo || undefined,
                    ...searchParam,
                    projectId: projectId.value,
                    customerId: customerId.value,
                    sn:
                        !projectId.value && !customerId.value
                            ? route.query.sn
                            : undefined,
                    //
                })
                dataSource.value = data.records
                paginationProps.value.total = data.total

                loading.value = false
                // loadingIndex += 1
            } catch (error) {
                loading.value = false
            }
        }

        const {
            paginationProps,
            changePage,
            onTableChange,
            refresh,
            pageParam,
        } = usePagenation(() => {
            PostDeviceAlarmPage(), PostDeviceAlarmStatisticalSummary()
        })

        const chart1Data = ref([])
        const chart2Data = ref([])
        const chart3Data = ref([])
        const formatData = (data, id, name) => {
            const chartDischargeDom = document.getElementById(id)
            chartDischargeDom && echarts.dispose(chartDischargeDom)
            echarts.init(chartDischargeDom).setOption(getPieOption(name, data))
        }
        const setPieChartOptions = () => {
            //
            formatData(
                chart1Data.value,
                'echarts-1',
                t('alarm_yichangzhuangtai')
            )
            formatData(chart2Data.value, 'echarts-2', t('yichangjibie'))
            formatData(chart3Data.value, 'echarts-3', t('Device alarm'))
        }
        // 图表接口--告警概览
        const PostDeviceAlarmStatisticalSummary = async () => {
            //搜索信息
            let searchParam = {
                // ...pageParam.value,
            }
            loading.value = true
            //搜索信息
            for (const key in searchData.fields) {
                if (
                    key == 'rangeDate' &&
                    searchData.fields.rangeDate.value?.length > 0
                ) {
                    searchParam.startDate = searchData.fields.rangeDate.value[0]
                    searchParam.endDate = searchData.fields.rangeDate.value[1]
                } else {
                    searchParam[key] = searchData.fields[key].value
                }
            }
            // //单个搜索信息
            searchParam[searchData.filterData.value.filterBy] =
                searchData.filterData.value.filterValue
            let params = {
                projectId: projectId.value,
                customerId: customerId.value,
                sn:
                    !projectId.value && !customerId.value
                        ? route.query.sn
                        : undefined,
                ...searchParam,
                // stationType: props.stationType,
            }
            const {
                data: { data },
            } = await powerApi.getStatisticalSummary(params)
            Object.keys(echartsObj).forEach((key) => {
                const ecahrtsDom = document.getElementById(key)
                // 严谨一点加了一个key判断
                if (key == 'echarts-1') {
                    const params = [
                        {
                            name: 0,
                            value: data?.alarmStatusQuantity?.unDisposedQuantity
                                ? data.alarmStatusQuantity.unDisposedQuantity
                                : 0,
                        },
                        // {
                        //     name: 2,
                        //     value: data?.alarmStatusQuantity?.clearQuantity
                        //         ? data.alarmStatusQuantity.clearQuantity
                        //         : 0,
                        // },
                        {
                            name: 1,
                            value: data?.alarmStatusQuantity?.recoverQuantity
                                ? data.alarmStatusQuantity.recoverQuantity
                                : 0,
                        },
                        // {
                        //     name: 3,
                        //     value: data?.alarmStatusQuantity?.reportedQuantity
                        //         ? data.alarmStatusQuantity.reportedQuantity
                        //         : 0,
                        // },
                    ]
                    const newParams = filterAssignment(params, key)

                    chart1Data.value = newParams
                    if (ecahrtsDom == null) {
                        return
                    }
                    echarts && echarts.dispose(ecahrtsDom)
                    echarts
                        .init(ecahrtsDom)
                        .setOption(
                            getPieOption(
                                t('alarm_yichangzhuangtai'),
                                chart1Data.value
                            )
                        )
                }
                if (key == 'echarts-2') {
                    const params = [
                        {
                            name: 3,
                            value: data?.alarmLevelQuantity?.urgencyQuantity
                                ? data.alarmLevelQuantity.urgencyQuantity
                                : 0,
                        },
                        {
                            name: 2,
                            value: data?.alarmLevelQuantity?.importantQuantity
                                ? data.alarmLevelQuantity.importantQuantity
                                : 0,
                        },
                        {
                            name: 1,
                            value: data?.alarmLevelQuantity?.minorQuantity
                                ? data.alarmLevelQuantity.minorQuantity
                                : 0,
                        },
                    ]
                    const newParams = filterAssignment(params, key)
                    chart2Data.value = newParams
                    if (ecahrtsDom == null) {
                        return
                    }
                    echarts && echarts.dispose(ecahrtsDom)
                    echarts
                        .init(ecahrtsDom)
                        .setOption(
                            getPieOption(t('yichangjibie'), chart2Data.value)
                        )
                }
                if (key == 'echarts-3') {
                    let params
                    if (data?.alarmDescQuantity?.length > 0) {
                        params = JSON.parse(
                            JSON.stringify(data.alarmDescQuantity)
                                .replace(/alarmQuantity/g, 'value')
                                .replace(/alarmDesc/g, 'name')
                        )
                    } else {
                        params = [
                            // {
                            //     name: '一级警告',
                            //     value: 0,
                            // },
                            // {
                            //     name: '二级警告',
                            //     value: 0,
                            // },
                            // {
                            //     name: '三级警告',
                            //     value: 0,
                            // },
                        ]
                    }
                    const newParams = filterAssignment(params, key)

                    chart3Data.value = newParams
                    if (ecahrtsDom == null) {
                        return
                    }
                    echarts && echarts.dispose(ecahrtsDom)
                    echarts
                        .init(ecahrtsDom)
                        .setOption(
                            getPieOption(t('Device alarm'), chart3Data.value)
                        )
                }
            })
        }

        const filterAssignment = (params, key) => {
            let data = echartsObj[key]
            const color = colors
            const newData = []
            for (let i = 0; i < params?.length; i++) {
                newData.push({
                    name:
                        key == 'echarts-3'
                            ? `${params[i].name} `
                            : `${data[i].label} `,
                    legendName:
                        key == 'echarts-3'
                            ? `${params[i].name}`
                            : `${data[i].label}`,
                    value: params[i].value,
                    label: {
                        show: false,
                    },
                    labelLine: {
                        // normal: {
                        show: false,
                        length: 15,
                        length2: 120,
                        lineStyle: {
                            color: 'transparent',
                        },
                        align: 'right',
                        // },
                        color: 'transparent',
                        emphasis: {
                            show: false,
                        },
                    },
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                            {
                                color:
                                    key == 'echarts-3'
                                        ? color[i].color
                                        : data[i].color,
                                offset: 0,
                            },
                            {
                                color:
                                    key == 'echarts-3'
                                        ? color[i].colorOffset
                                        : data[i].colorOffset,
                                offset: 1,
                            },
                        ]),
                        borderColor: '#fff',
                        borderWidth: 2,
                    },
                    textStyle: {
                        width: 60,
                        overflow: 'truncate',
                    },
                })
            }

            return newData
        }

        // 弹出详情

        const Rowclick = (record) => {
            return {
                onClick: (event) => {
                    //   router.push({
                    //     name: "authCenterDetail",
                    //     query: { id: record.id, bizType: record.bizType },
                    //   });
                    alarmId.value = record.id
                    alarmOverviewDetailDrawerSwitch.value = true
                }, // 点击行
            }
        }

        // const handleTabChange = (key)=>{
        //     show.value = key == '1'?true:false
        // }

        const orgId = ref(
            getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
        )
        const stations = ref([])
        const getStations = async () => {
            const page = {
                orgId: orgId.value,
                stationType: 'vehicle_battery',
            }
            const res = await api.getOrgAndSubOrgStationNameList(page)
            stations.value = res.data.data.map((item) => ({
                label: item.stationName,
                value: item.stationNo,
            }))
        }
        watch(
            getAlarmDataFlag,
            async (val) => {
                if (val) {
                    //
                    // getStations()
                    PostDeviceAlarmStatisticalSummary()
                    PostDeviceAlarmPage()
                }
            },
            { immediate: true, deep: true }
        )
        const { themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                setPieChartOptions()
            }
        })
        const getAlarmStatus = (val) => {
            const item =
                alarmStatusList.value.find((s) => s.value === val) || {}
            return item
        }

        return {
            dataSource,
            columns,
            // pageConfig,
            alarmStatusList,
            alarmLevelList,
            deviceTypeList,
            searchData,
            refresh,
            changePage,
            paginationProps,
            onTableChange,
            Rowclick,
            alarmOverviewDetailDrawerSwitch,
            alarmId,
            // alarmOverviewDetailDrawerSwitch1,
            orgNameValue,
            loading,
            // tabList,
            dayjs,
            chart1Data,
            chart2Data,
            chart3Data,
            stations,
            getAlarmStatus,
        }
    },
}
</script>

<style scoped lang="less">
.echarts-all-box {
    width: 100%;
    display: flex;

    .charts-box {
        padding: 32px 20px;
        // padding-right: 80px;
        border-radius: 8px;
        // background: var(--car-pie-border);
        border: 1px solid #f5f5f5;
    }

    @media screen and (max-width: 1560px) {
        .charts-box {
            padding-right: 32px;
        }
    }

    .ecahrts-dom {
        width: 200px;
        height: 200px;
        margin-right: 32px;

        // width: 33.33%;
        &:last-child {
            margin-right: 0;
        }
    }
}

.table {
    margin-top: 12px;
    padding-top: 16px;

    :deep(.ant-pagination) {
        text-align: center !important;
    }

    .table-box {
        padding-top: 2px;
    }
}

.drawer-overview {
    .ant-drawer-body {
        padding: 0;
    }
}

.cursor-pointer {
    font-size: 0.9rem;
    color: #222222;

    span {
        img {
            vertical-align: sub;
            width: 1rem;
        }
    }
}

.go-box {
    .bt-box-go {
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        background-color: #fff;
        text-align: center;

        &:hover {
            background-color: var(--themeColor);
            color: #fff;
        }
    }
}

.my-m-t-2 {
    margin-top: 8px;
}

.my-m-t-3 {
    margin-top: 12px;
}

.my-m-b-4 {
    margin-bottom: 16px;
}

.my-m-l-2 {
    margin-left: 8px;
}

.my-rounded-lg {
    border-radius: 8px;
}

.search-top {
    padding: 5px 16px 0 16px;

    :deep(.font-medium) {
        color: #999999;
    }

    :deep(.ant-select) {
        font-size: 14px;
        width: 160px !important;
    }

    :deep(.ant-select-selector) {
        height: 32px;
        padding: 0 11px;

        .ant-select-selection-search-input {
            height: 30px;
        }

        .ant-select-selection-item {
            line-height: 30px;
        }

        .ant-select-selection-placeholder {
            line-height: 30px;
        }
    }
}

.teble-search {
    padding: 0 8px !important;

    :deep(.font-medium) {
        color: #999999;
    }

    :deep(.ant-select) {
        font-size: 14px;
        width: 160px !important;

        &:hover {
            .ant-select-selector {
                border-color: var(--themeColor);
                box-shadow: none;
            }
        }
    }

    :deep(.ant-select-focused) {
        .ant-select-selector {
            border-color: var(--themeColor) !important;
            box-shadow: none !important;
        }
    }

    :deep(.ant-select-arrow) {
        right: 11px;
        width: 12px;
        height: 12px;
        margin-top: -6px;
        font-size: 12px;
    }

    :deep(.ant-select-clear) {
        width: 12px;
        height: 12px;
        margin-top: -6px;
        right: 11px;
        font-size: 12px;
    }

    :deep(.ant-select-selector) {
        height: 32px;
        padding: 0 11px;

        .ant-select-selection-search-input {
            height: 30px;
        }

        .ant-select-selection-item {
            line-height: 30px;
        }

        .ant-select-selection-placeholder {
            line-height: 30px;
        }
    }

    :deep(.ant-input-group) {
        .ant-select {
            display: none;
        }
    }

    :deep(.ant-input-search) {
        padding: 4px 11px;
        height: 32px;
        box-sizing: border-box;

        input {
            font-size: 14px;
        }

        .ant-input-suffix {
            margin-left: 4px;

            svg {
                width: 14px;
                height: 14px;
            }
        }
    }

    :deep(.ant-input-affix-wrapper) {
        &:hover {
            border-color: var(--themeColor);
            box-shadow: none;
        }
    }

    :deep(.ant-input-affix-wrapper-focused) {
        border-color: var(--themeColor);
        box-shadow: none;
    }

    :deep(.ant-calendar-picker) {
        width: 240px !important;

        .ant-calendar-picker-input {
            padding: 0px 11px;
            height: 32px;
            box-sizing: border-box;

            .ant-calendar-range-picker-input {
                font-size: 14px;
            }

            .ant-calendar-picker-icon {
                right: 5px;
                font-size: 14px;
                // top: 55%;
            }

            .ant-calendar-range-picker-separator {
                min-width: 10px;
                vertical-align: baseline !important;
            }

            .ant-calendar-picker-clear {
                right: 5px;
                font-size: 14px;
                top: 50%;
            }
        }

        .ant-input {
            &:hover {
                border-color: var(--themeColor);
            }
        }

        &:focus {
            .ant-calendar-picker-input {
                &:not(.ant-input-disabled) {
                    border-color: var(--themeColor) !important;
                    box-shadow: none;
                }
            }
        }
    }
}

.alarm-name {
    line-height: 24px;
}

.alarm-sn {
    font-size: 14px;
    line-height: 22px;
}

.alarm-table {
    :deep(.text) {
        font-size: 14px !important;
        color: #595959 !important;
    }
}

.blockTabs {
    :deep(.ant-tabs-bar) {
        margin-bottom: 0;
        border-bottom-width: 0;

        .ant-tabs-tab {
            font-size: 16px;
            padding: 22px 0 10px 16px;
            margin-right: 16px;
        }

        .ant-tabs-ink-bar {
            // width: 32px !important;
            height: 4px;
            margin-left: 30px;
        }
    }
}

:deep(.ant-table-thead tr th) {
    // color: rgba(34, 34, 34, 0.6) !important;
}

:deep(.ant-table-tbody .ant-table-row td) {
    vertical-align: top;
}
.drawer-container-bg {
    background-color: var(--header-bg);
}
</style>

<style lang="less">
:deep(.tree-svg-box) {
    width: 20px;
    height: 20px;
    margin-right: 14px;
}
</style>

// 树节点魔改
<style lang="less">
.tree-nodes {
    background: #f5f7f7;
    padding: 12px;
    border-radius: 4px;
}

.ant-tree li .ant-tree-node-content-wrapper {
    display: block;
    width: 100% !important;
    padding-right: 30px;
    height: 44px;
    line-height: 44px;
}

.ant-tree li span.ant-tree-switcher {
    position: absolute;
    right: 10px;
    top: 12px;
}

ul.ant-tree > li > .ant-tree-node-content-wrapper {
    margin-bottom: 10px;
}

ul.ant-tree > li:last-child > .ant-tree-node-content-wrapper {
    margin-bottom: 0;
}

ul.ant-tree > li > ul {
    background: #fff;
    border-radius: 4px;
    margin-top: 10px;
}

.ant-tree li ul {
    padding: 12px;
}

.ant-tree li {
    width: 100%;
    position: relative;
    padding: 0;
}

.ant-tree-child-tree > li:first-child {
    padding: 0;
}

.ant-tree li + li {
    margin-top: 10px;
}

ul.ant-tree > li > ul > li {
    background: #f5f7f7;
    border-radius: 4px;
}

.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    border-radius: 4px;
}

.ant-tree > li:last-child {
    padding: 0;
}

.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background: var(--themeColor);
    color: #fff;
}

.tree-nodes {
    .ant-tree-treenode-selected {
        > .ant-tree-switcher {
            svg {
                color: #fff !important;
            }
        }
    }
}
</style>
