<template>
    <div class="cursor-pointer">
        <charge-title
            :charge-data="data.yesterdayChargeDur"
            :charge-unit="'h'"
            :discharge-data="data.yesterdayDischargeDur"
            :discharge-unit="'h'"
        />
        <div
            class="flex justify-between items-center px-3"
            style="line-height: 42px"
        >
            <div class="flex-1 text-left">
                <span class="text-secondar-text dark:text-60-dark">
                    {{
                        data?.beforeYesterdayChargeDur == 0
                            ? $t('Previous day: No data')
                            : $t('station_jiaoqianyiri') + ': '
                    }}
                </span>
                <percentage :num="data.comparedChargePercent" class="ml-0" />
            </div>
            <div class="flex-1 text-right">
                <span class="text-secondar-text dark:text-60-dark">
                    {{
                        data?.beforeYesterdayDischargeDur == 0
                            ? $t('Previous day: No data')
                            : $t('station_jiaoqianyiri') + ': '
                    }}
                </span>
                <percentage :num="data.comparedDischargePercent" class="ml-0" />
            </div>
        </div>
        <a-divider class="m-0" />
        <div class="flex justify-between items-center px-3 mt-3 leading-4">
            <div class="flex-1 text-left flex">
                <div>
                    <span class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_yueleiji') }}:
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.currentMonthChargeDur }}
                        <span class="text-xs">h</span>
                    </span>
                </div>
                <div class="ml-4">
                    <span class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_zongji') }}:
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.totalChargeDur }}
                        <span class="text-xs">h</span>
                    </span>
                </div>
            </div>
            <div class="flex-1 text-right flex justify-end">
                <div>
                    <span class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_yueleiji') }}:
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.currentMonthDischargeDur }}
                        <span class="text-xs">h</span>
                    </span>
                </div>
                <div class="ml-4">
                    <span class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_zongji') }}:
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.totalDischargeDur }}
                        <span class="text-xs">h</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import chargeTitle from '../components/chargeTitle.vue'
import Percentage from './percentage.vue'
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})
</script>

<style lang="less" scoped>
.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 24px');
    position: relative;
}

.charge-title-l {
    background: rgba(51, 190, 79, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 43px solid transparent;
    }
}

.charge-title-r {
    background: rgba(119, 155, 219, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(119, 155, 219, 0.1);
        border-left: 43px solid transparent;
    }
}
</style>
