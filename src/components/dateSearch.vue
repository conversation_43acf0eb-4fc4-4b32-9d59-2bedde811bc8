<template>
    <el-config-provider>
        <div class="flex items-center">
            <template v-if="info.periodOptions?.length">
                <el-select
                    v-model="periodType"
                    placeholder="请选择"
                    style="margin-right: 12px; border-radius: 8px"
                    :style="{ width: locale == 'en' ? '180px' : '120px' }"
                    @change="onPeriodTypeChange"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </template>
            <el-date-picker
                v-model="dayDate"
                :type="isTimerPicker ? 'datetime' : 'date'"
                :placeholder="$t('Date Select')"
                :value-format="
                    isTimerPicker ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
                "
                :style="{ width: !isTimerPicker ? '150px' : '240px' }"
                v-if="
                    info.periodOptions?.length
                        ? periodType == 'hour'
                        : info.datePickerType == 'hour'
                "
                @change="dateChange"
                :clearable="false"
            />
            <el-date-picker
                v-model="rangeDate"
                :type="isTimerPicker ? 'datetimerange' : 'daterange'"
                range-separator="-"
                :start-placeholder="$t('common_kaishiriqi')"
                :end-placeholder="$t('common_jieshuriqi')"
                :value-format="
                    isTimerPicker ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
                "
                @calendar-change="onCalendarChange"
                :disabled-date="disabledDates"
                @change="rangeDateChange"
                @visible-change="onVisibleChange"
                :style="{ width: !isTimerPicker ? '240px' : '388px' }"
                :clearable="false"
                v-if="
                    info.periodOptions?.length
                        ? periodType == 'day' || periodType == 'minute'
                        : info.datePickerType == 'day' ||
                          info.datePickerType == 'minute'
                "
            />
            <el-date-picker
                v-model="rangeMonth"
                type="monthrange"
                range-separator="-"
                :start-placeholder="$t('kaishiyuefen')"
                :end-placeholder="$t('jieshuyuefen')"
                value-format="YYYY-MM"
                @calendar-change="onCalendarChangeM"
                @change="rangeDateChangeM"
                @visible-change="onVisibleChangeM"
                :disabled-date="disabledMonths"
                style="width: 240px"
                :clearable="false"
                v-if="
                    info.periodOptions?.length
                        ? periodType == 'month'
                        : info.datePickerType == 'month'
                "
            />
        </div>
    </el-config-provider>
</template>

<script setup>
import { ref, watch, computed, nextTick } from 'vue'
// import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import moment from 'moment'
import { periodTypes } from '@/views/device/const.js'
import { useI18n } from 'vue-i18n'
const props = defineProps({
    // 时段类型  // [ 'minute', 'hour','day','month']
    info: {
        type: Object,
        default: () => ({
            minDate: '1971-01-01',
            dayRangeLen: 30,
            defaultDayRangeLen: 7,
        }),
    },
    periodOptions: {
        type: Array,
        default: () => [],
    },
    // 日期选择器类型   "periodOptions"长度为0时生效 // 'minute', 'hour','day','month'
    datePickerType: {
        type: String,
        default: '',
    },
    defaultPickerType: {
        type: String,
        default: '',
    },

    dateSelect: {
        type: Object,
        default: () => ({}),
    },
    defaultDayRangeLen: {
        type: Number,
        default: 7, // 默认日区间长度
    },
    dayRangeLen: {
        type: Number,
        default: 30, // 可选择日区间长度
    },
    // 月区间长度
    monthRangeLen: {
        type: Number,
        default: 12,
    },
    isTimerPicker: {
        type: Boolean,
        default: false,
    },
})
const { t, locale } = useI18n()
const emits = defineEmits(['onChange', 'update:dateSelect'])
const options = ref([])
const setDefaultDates = (type) => {
    // if (props.dateSelect.startDate && props.dateSelect.endDate) {
    //     rangeDate.value = [
    //         props.dateSelect?.startDate,
    //         props.dateSelect?.endDate,
    //     ]
    // } else {
    if (type == 'day') {
        rangeDate.value = props.dateSelect?.startDate
            ? [props.dateSelect?.startDate, props.dateSelect?.endDate]
            : detaultRangeDate.value
    } else if (type == 'month') {
        rangeMonth.value = defaultRangeMonth.value
    } else if (type == 'hour') {
        dayDate.value = defaultDayDate.value
    } else if (type == 'minute') {
        rangeDate.value = detaultRangeDate.value
    }
    // }
}
const dayDate = ref(null)
const periodType = ref()

// 单日切换 ⬇️
const defaultDayDate = computed(() => {
    return props.isTimerPicker
        ? moment().format('YYYY-MM-DD HH:mm:ss')
        : moment().format('YYYY-MM-DD')
})
const dateChange = () => {
    //
    changeData(false)
    change()
}

// 单日切换 ⬆️

// 日期选择  ⬇️
const detaultRangeDate = computed(() => {
    const dateFormat = props.isTimerPicker
        ? 'YYYY-MM-DD HH:mm:ss'
        : 'YYYY-MM-DD'

    if (props.info.setNull) {
        return []
    }
    if (props.info.minDate) {
        let date = moment().subtract(
            props.info.defaultDayRangeLen || props.info.dayRangeLen || 30,
            'days'
        ) // 当前天往前推（ props.info.dayRangeLen || 30）天
        // 如果早于安装时间 则取[安装时间,当前天]
        if (date.isBefore(moment(props.info.minDate))) {
            return [
                moment(props.info.minDate).format(dateFormat),
                moment().format(dateFormat),
            ]
        }

        // 否则  取[当前天往前推（ props.info.dayRangeLen || 30）天,当前天]
        return [
            moment()
                .subtract(
                    props.info.defaultDayRangeLen ||
                        props.info.dayRangeLen ||
                        30,
                    'days'
                )
                .format(dateFormat),
            moment().format(dateFormat),
        ]
    }
    return [
        moment()
            .subtract(props.info.dayRangeLen || 30, 'days')
            .format(dateFormat),
        moment().format(dateFormat),
    ]
})
const rangeDate = ref(detaultRangeDate.value)
const rangeDateChange = (value) => {
    //
    changeData(false)
    change()
}
const dates = ref([])

const onCalendarChange = (val) => {
    if (props.isTimerPicker) {
        let newVal
        let newRangeDate
        newVal = [
            val[0]
                ? new Date(moment(val[0]).format('YYYY-MM-DD 00:00:00'))
                : val[0],
            val[1]
                ? new Date(moment(val[1]).format('YYYY-MM-DD 23:59:59'))
                : val[1],
        ]
        newRangeDate = [
            moment(val[0]).format('YYYY-MM-DD 00:00:00'),
            moment(val[1]).format('YYYY-MM-DD 23:59:59'),
        ]
        dates.value = newVal
        // 如果是日期时间选择器，需要设置默认时间
        nextTick(() => {
            if (val[1]) rangeDate.value = newRangeDate
        })
    } else {
        dates.value = val
    }
}

const onVisibleChange = (e) => {
    if (dates.value && dates.value[1]) {
        dates.value = null
    }
    if (!e) {
        dates.value = null
    }
}
const disabledDates = (current) => {
    // 如果没有选中日期，禁用范围是  小于安装时间，大于当前时间
    if (!dates.value || dates.value.length === 0) {
        return (
            current.getTime() <=
                new Date(props.info.minDate || '1971-01-01').getTime() -
                    86400000 || current.getTime() > Date.now()
        )
    }
    // 如果选中了日期，禁用范围条件增加 ：30天以内
    const diffDate = moment(current).diff(dates.value[0], 'days')
    return (
        Math.abs(diffDate) > (props.info.dayRangeLen || 30) ||
        current.getTime() <=
            new Date(props.info.minDate || '1971-01-01').getTime() - 86400000 ||
        current.getTime() > Date.now()
    )
}

// 日期选择⬆️
// 月选择  ⬇️
const defaultRangeMonth = computed(() => {
    if (props.info.minDate) {
        let month = moment().subtract(props.info.monthRangeLen || 11, 'months') // 当前月份往前推（props.info.monthRangeLen || 11）个月
        // 如果早于安装时间的月份 则取[安装时间所在月份,当前月份]
        if (month.isBefore(moment(props.info.minDate).startOf('month'))) {
            return [
                moment(props.info.minDate).format('YYYY-MM'),
                moment().format('YYYY-MM'),
            ]
        }
        // 否则  取[当前月份往前推（props.info.monthRangeLen || 11）个月,当前月份]
        return [
            moment()
                .subtract(props.info.monthRangeLen || 11, 'months')
                .format('YYYY-MM'),
            moment().format('YYYY-MM'),
        ]
    }
    return [
        moment()
            .subtract(props.info.monthRangeLen || 11, 'months')
            .format('YYYY-MM'),
        moment().format('YYYY-MM'),
    ]
})
const rangeMonth = ref(defaultRangeMonth.value)
const months = ref()
const onCalendarChangeM = (val) => {
    months.value = val
}
const onVisibleChangeM = (e) => {
    if ((months.value && months.value[1]) || !e) {
        months.value = null
    }
}
const disabledMonths = (current) => {
    // 如果没有选中月份，禁用范围是 小于安装时间，大于当前时间
    if (!months.value || months.value.length === 0) {
        return (
            moment(current).isBefore(
                moment(props.info.minDate || '1971-01-01').startOf('month')
            ) || moment(current).isAfter(moment().endOf('month'))
        )
    }

    // 如果选中了月份，禁用范围条件增加：范围之外的月份
    const diffMonths = moment(current).diff(months.value[0], 'months')
    return (
        Math.abs(diffMonths) >
            (props.info.defaultMonthRangeLen ||
                props.info.monthRangeLen ||
                12) ||
        moment(current).isBefore(
            moment(props.info.minDate || '1971-01-01').startOf('month')
        ) ||
        moment(current).isAfter(moment().endOf('month'))
    )
}
const rangeDateChangeM = () => {
    //
    changeData(false)
    change()
}
// 月选择  ⬆️

// ⬇️  ⬇️  ⬇️ 设置参数  ⬇️  ⬇️  ⬇️
const setParams = (type, start, end) => ({
    periodType: type,
    ...(type === 'day' || type === 'minute' || type === 'hour'
        ? { startDate: start, endDate: end }
        : { startMonth: start, endMonth: end }),
})
// function setParams(type, rangeStart, rangeEnd) {

//     if (type === 'day') {
//         return { periodType: type, startDate: rangeStart, endDate: rangeEnd }
//     } else if (type === 'month') {
//         return {
//             periodType: type,
//             startMonth: rangeStart,
//             endMonth: rangeEnd,
//         }
//     } else if (type === 'hour') {
//         return { periodType: type, startDate: rangeStart, endDate: rangeEnd }
//     } else if (type === 'minute') {
//         return { periodType: type, startDate: rangeStart, endDate: rangeEnd }
//     }
//     return {}
// }

// 修改选项时设置默认时间时间段
function resetRange(type, change) {
    if ((type === 'day' || type === 'minute') && change) {
        rangeDate.value = detaultRangeDate.value
    } else if (type === 'month' && change) {
        rangeMonth.value = defaultRangeMonth.value
    } else if (type === 'hour' && change) {
        dayDate.value = defaultDayDate.value
    }
}
const onUpdate = () => {
    emits('update:dateSelect', params.value)
}
const change = () => {
    emits('onChange', params.value)
}
const params = ref()
// 切换选项时
const changeData = (changeType) => {
    if (periodType.value) {
        resetRange(periodType.value, changeType)
        if (periodType.value === 'day' || periodType.value === 'minute') {
            params.value = setParams(
                periodType.value,
                rangeDate.value[0],
                rangeDate.value[1]
            )
        } else if (periodType.value === 'month') {
            params.value = setParams(
                periodType.value,
                rangeMonth.value[0],
                rangeMonth.value[1]
            )
        } else if (periodType.value === 'hour') {
            params.value = setParams(
                periodType.value,
                dayDate.value,
                dayDate.value
            )
        }
    } else {
        if (
            props.info.datePickerType === 'day' ||
            props.info.datePickerType === 'minute'
        ) {
            params.value = setParams(
                props.info.datePickerType,
                rangeDate.value[0],
                rangeDate.value[1]
            )
        } else if (props.info.datePickerType === 'month') {
            params.value = setParams(
                props.info.datePickerType,
                rangeMonth.value[0],
                rangeMonth.value[1]
            )
        } else if (props.info.datePickerType === 'hour') {
            params.value = setParams(
                props.info.datePickerType,
                dayDate.value,
                dayDate.value
            )
        }
    }

    // 向父组件发送事件
    onUpdate()
    //
}
// ⬆️  ⬆️  ⬆️ 设置参数  ⬆️  ⬆️  ⬆️

// 切换时段类型
const onPeriodTypeChange = (e) => {
    changeData(true)
    change()
}
watch(
    () => props.info,
    (newVal, oldVal) => {
        if (!newVal || JSON.stringify(newVal) === JSON.stringify(oldVal)) return
        options.value = periodTypes.filter((item) =>
            newVal.periodOptions.includes(item.value)
        )
        const pickerType =
            newVal.defaultPickerType ||
            newVal.periodOptions[0] ||
            props.info.datePickerType
        periodType.value = newVal.periodOptions.length ? pickerType : null
        setDefaultDates(pickerType)
        changeData(newVal.periodOptions.length > 0)
    },
    { immediate: true }
)
</script>

<style lang="less" scoped>
:deep(.el-date-editor .el-range-separator) {
    width: 20px;
}
:deep(.el-range__close-icon--hidden) {
    display: none !important;
}
</style>
